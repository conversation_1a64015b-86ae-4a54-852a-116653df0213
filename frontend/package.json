{"name": "yearbook-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@editorjs/editorjs": "^2.30.6", "@editorjs/header": "^2.8.7", "@editorjs/image": "^2.9.3", "@editorjs/list": "^1.9.0", "@editorjs/paragraph": "^2.11.6", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.51.23", "@tanstack/react-query-devtools": "^5.51.23", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.424.0", "next": "^14.2.32", "next-intl": "^4.3.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.2", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.14", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "postcss": "^8.4.41", "prettier": "^3.3.3", "tailwindcss": "^3.4.7", "typescript": "^5.5.4"}}
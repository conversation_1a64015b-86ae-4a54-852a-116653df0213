TBDREADME.md
Backend: Digital Yearbook Platform
This directory contains the backend services for the Digital Yearbook Platform. It is responsible for handling data storage, business logic, and PDF generation.

1. Technology Stack
For detailed information on the technologies used, please refer to the tech-stack.md file.

2. Architecture
For a comprehensive overview of the backend architecture and service interactions, please refer to the architecture.md file.

3. Folder Structure
The backend is structured to ensure a clear separation of concerns, making it easy to navigate and maintain.

4. Getting Started
Follow these steps to set up and run the backend locally.

Prerequisites

 (for Supabase services)

Installation
Clone the repository and navigate to the backend directory.

Restore the .NET dependencies:

Set up your local Supabase instance using Docker. Follow the instructions in the Supabase documentation to get your local API keys and connection strings.

Copy .env.example to .env and fill in your Supabase connection details.

Running the Development Server
Run the application with the following command:

The API will be available at the specified port in your configuration (e.g., http://localhost:5000).
